import React, { useState } from "react";

const ItineraryForm = () => {
  const [numDays, setNumDays] = useState(1);
  const [days, setDays] = useState([
    {
      activities: [],
      transfers: [],
      flights: [],
    },
  ]);

  const handleNumDaysChange = (e) => {
    const newNumDays = parseInt(e.target.value);
    setNumDays(newNumDays);
    const updatedDays = [...days];

    // add new day objects if needed
    while (updatedDays.length < newNumDays) {
      updatedDays.push({
        activities: [],
        transfers: [],
        flights: [],
      });
    }
    // remove days if fewer now
    while (updatedDays.length > newNumDays) {
      updatedDays.pop();
    }
    setDays(updatedDays);
  };

  const addActivity = (dayIndex) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].activities.push({
      id: "",
      name: "",
      description: "",
      price: "",
    });
    setDays(updatedDays);
  };

  const updateActivity = (dayIndex, activityIndex, field, value) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].activities[activityIndex][field] = value;
    setDays(updatedDays);
  };

  const addTransfer = (dayIndex) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].transfers.push({
      type: "",
      time: "",
      price: "",
      peopleAllowed: "",
    });
    setDays(updatedDays);
  };

  const updateTransfer = (dayIndex, transferIndex, field, value) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].transfers[transferIndex][field] = value;
    setDays(updatedDays);
  };

  const addFlight = (dayIndex) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].flights.push({
      flightNumber: "",
      airline: "",
      departureTime: "",
      arrivalTime: "",
      price: "",
    });
    setDays(updatedDays);
  };

  const updateFlight = (dayIndex, flightIndex, field, value) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].flights[flightIndex][field] = value;
    setDays(updatedDays);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Final Itinerary:", days);
    alert("Itinerary Saved!");
  };

  return (
    <div className="max-w-6xl mx-auto p-8 bg-white rounded shadow space-y-10">
      <h1 className="text-3xl font-bold text-[#541C9C] text-center mb-6">
        Create Travel Itinerary
      </h1>

      <div className="space-y-4">
        <label className="block text-gray-700 font-medium">
          How many days in the itinerary?
        </label>
        <input
          type="number"
          min="1"
          value={numDays}
          onChange={handleNumDaysChange}
          className="w-32 rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
        />
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {days.map((day, dayIndex) => (
          <div key={dayIndex} className="border border-gray-200 p-6 rounded space-y-6">
            <h2 className="text-2xl font-bold text-[#321E5D]">
              Day {dayIndex + 1}
            </h2>

            {/* Activities */}
            <div>
              <h3 className="text-lg font-semibold mb-4 text-[#541C9C] flex justify-between items-center">
                Activities
                <button
                  type="button"
                  onClick={() => addActivity(dayIndex)}
                  className="bg-[#541C9C] text-white text-sm px-3 py-1 rounded"
                >
                  + Add Activity
                </button>
              </h3>

              {day.activities.map((activity, activityIndex) => (
                <div
                  key={activityIndex}
                  className="p-4 border rounded space-y-3 mb-4"
                >
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm text-gray-700">
                        Activity ID
                      </label>
                      <input
                        value={activity.id}
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "id",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm text-gray-700">
                        Activity Name
                      </label>
                      <input
                        value={activity.name}
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "name",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div className="sm:col-span-2">
                      <label className="block text-sm text-gray-700">
                        Description
                      </label>
                      <textarea
                        value={activity.description}
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "description",
                            e.target.value
                          )
                        }
                        rows={2}
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      ></textarea>
                    </div>
                    <div>
                      <label className="block text-sm text-gray-700">
                        Price
                      </label>
                      <input
                        value={activity.price}
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "price",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Transfer */}
            <div>
              <h3 className="text-lg font-semibold mb-4 text-[#541C9C] flex justify-between items-center">
                Transfers
                <button
                  type="button"
                  onClick={() => addTransfer(dayIndex)}
                  className="bg-[#541C9C] text-white text-sm px-3 py-1 rounded"
                >
                  + Add Transfer
                </button>
              </h3>
              {day.transfers.map((transfer, transferIndex) => (
                <div key={transferIndex} className="border p-4 rounded space-y-3 mb-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm text-gray-700">Transfer Type</label>
                      <input
                        value={transfer.type}
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "type", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm text-gray-700">Timings</label>
                      <input
                        value={transfer.time}
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "time", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm text-gray-700">Price</label>
                      <input
                        value={transfer.price}
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "price", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm text-gray-700">
                        People Allowed
                      </label>
                      <input
                        value={transfer.peopleAllowed}
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "peopleAllowed", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Flights */}
            <div>
              <h3 className="text-lg font-semibold mb-4 text-[#541C9C] flex justify-between items-center">
                Flights
                <button
                  type="button"
                  onClick={() => addFlight(dayIndex)}
                  className="bg-[#541C9C] text-white text-sm px-3 py-1 rounded"
                >
                  + Add Flight
                </button>
              </h3>
              {day.flights.map((flight, flightIndex) => (
                <div key={flightIndex} className="border p-4 rounded space-y-3 mb-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm text-gray-700">Flight Number</label>
                      <input
                        value={flight.flightNumber}
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "flightNumber", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm text-gray-700">Airline</label>
                      <input
                        value={flight.airline}
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "airline", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm text-gray-700">Departure Time</label>
                      <input
                        value={flight.departureTime}
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "departureTime", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm text-gray-700">Arrival Time</label>
                      <input
                        value={flight.arrivalTime}
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "arrivalTime", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm text-gray-700">Price</label>
                      <input
                        value={flight.price}
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "price", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}

        <button
          type="submit"
          className="w-full bg-[#541C9C] text-white py-3 rounded hover:bg-[#680099] font-semibold"
        >
          Save Itinerary
        </button>
      </form>
    </div>
  );
};

export default ItineraryForm;
