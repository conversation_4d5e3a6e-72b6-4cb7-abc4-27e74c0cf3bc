import React, { useState } from "react";

const ItineraryForm = () => {
  const [numDays, setNumDays] = useState(1);
  const [days, setDays] = useState([
    {
      activities: [],
      transfers: [],
      flights: [],
      accommodations: [],
    },
  ]);

  const handleNumDaysChange = (e) => {
    const newNumDays = parseInt(e.target.value);
    setNumDays(newNumDays);
    const updatedDays = [...days];

    // add new day objects if needed
    while (updatedDays.length < newNumDays) {
      updatedDays.push({
        activities: [],
        transfers: [],
        flights: [],
        accommodations: [],
      });
    }
    // remove days if fewer now
    while (updatedDays.length > newNumDays) {
      updatedDays.pop();
    }
    setDays(updatedDays);
  };

  const addActivity = (dayIndex) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].activities.push({
      id: "",
      name: "",
      description: "",
      price: "",
      duration: "",
      startTime: "",
      endTime: "",
      location: "",
      image: "",
      inclusions: "",
      exclusions: "",
    });
    setDays(updatedDays);
  };

  const updateActivity = (dayIndex, activityIndex, field, value) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].activities[activityIndex][field] = value;
    setDays(updatedDays);
  };

  const addTransfer = (dayIndex) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].transfers.push({
      type: "",
      vehicleType: "",
      from: "",
      to: "",
      departureTime: "",
      arrivalTime: "",
      price: "",
      peopleAllowed: "",
      duration: "",
      distance: "",
    });
    setDays(updatedDays);
  };

  const updateTransfer = (dayIndex, transferIndex, field, value) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].transfers[transferIndex][field] = value;
    setDays(updatedDays);
  };

  const addFlight = (dayIndex) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].flights.push({
      flightNumber: "",
      airline: "",
      from: "",
      to: "",
      departureTime: "",
      arrivalTime: "",
      price: "",
      duration: "",
      class: "",
      baggage: "",
    });
    setDays(updatedDays);
  };

  const addAccommodation = (dayIndex) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].accommodations.push({
      name: "",
      type: "",
      location: "",
      checkIn: "",
      checkOut: "",
      roomType: "",
      price: "",
      rating: "",
      amenities: "",
      image: "",
    });
    setDays(updatedDays);
  };

  const updateFlight = (dayIndex, flightIndex, field, value) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].flights[flightIndex][field] = value;
    setDays(updatedDays);
  };

  const updateAccommodation = (dayIndex, accommodationIndex, field, value) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].accommodations[accommodationIndex][field] = value;
    setDays(updatedDays);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Final Itinerary:", days);
    alert("Itinerary Saved!");
  };

  return (
    <div className="max-w-6xl mx-auto p-8 bg-white rounded shadow space-y-10">
      <h1 className="text-3xl font-bold text-[#541C9C] text-center mb-6">
        Create Travel Itinerary
      </h1>

      <div className="space-y-4">
        <label className="block text-gray-700 font-medium">
          How many days in the itinerary?
        </label>
        <input
          type="number"
          min="1"
          value={numDays}
          onChange={handleNumDaysChange}
          className="w-32 rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
        />
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {days.map((day, dayIndex) => (
          <div key={dayIndex} className="border border-gray-200 p-6 rounded space-y-6">
            <h2 className="text-2xl font-bold text-[#321E5D]">
              Day {dayIndex + 1}
            </h2>

            {/* Activities */}
            <div>
              <h3 className="text-lg font-semibold mb-4 text-[#541C9C] flex justify-between items-center">
                Activities
                <button
                  type="button"
                  onClick={() => addActivity(dayIndex)}
                  className="bg-[#541C9C] text-white text-sm px-3 py-1 rounded"
                >
                  + Add Activity
                </button>
              </h3>

              {day.activities.map((activity, activityIndex) => (
                <div
                  key={activityIndex}
                  className="p-4 border rounded space-y-3 mb-4 bg-gray-50"
                >
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Activity ID
                      </label>
                      <input
                        value={activity.id}
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "id",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Activity Name
                      </label>
                      <input
                        value={activity.name}
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "name",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Location
                      </label>
                      <input
                        value={activity.location}
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "location",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Start Time
                      </label>
                      <input
                        type="time"
                        value={activity.startTime}
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "startTime",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        End Time
                      </label>
                      <input
                        type="time"
                        value={activity.endTime}
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "endTime",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Duration
                      </label>
                      <input
                        value={activity.duration}
                        placeholder="e.g., 2 hours"
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "duration",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Price
                      </label>
                      <input
                        value={activity.price}
                        placeholder="e.g., $50"
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "price",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Image URL
                      </label>
                      <input
                        value={activity.image}
                        placeholder="https://..."
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "image",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div className="lg:col-span-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Description
                      </label>
                      <textarea
                        value={activity.description}
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "description",
                            e.target.value
                          )
                        }
                        rows={2}
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      ></textarea>
                    </div>
                    <div className="lg:col-span-3">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            Inclusions
                          </label>
                          <textarea
                            value={activity.inclusions}
                            placeholder="What's included..."
                            onChange={(e) =>
                              updateActivity(
                                dayIndex,
                                activityIndex,
                                "inclusions",
                                e.target.value
                              )
                            }
                            rows={2}
                            className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                          ></textarea>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            Exclusions
                          </label>
                          <textarea
                            value={activity.exclusions}
                            placeholder="What's not included..."
                            onChange={(e) =>
                              updateActivity(
                                dayIndex,
                                activityIndex,
                                "exclusions",
                                e.target.value
                              )
                            }
                            rows={2}
                            className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                          ></textarea>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Transfer */}
            <div>
              <h3 className="text-lg font-semibold mb-4 text-[#541C9C] flex justify-between items-center">
                Transfers
                <button
                  type="button"
                  onClick={() => addTransfer(dayIndex)}
                  className="bg-[#541C9C] text-white text-sm px-3 py-1 rounded"
                >
                  + Add Transfer
                </button>
              </h3>
              {day.transfers.map((transfer, transferIndex) => (
                <div key={transferIndex} className="border p-4 rounded space-y-3 mb-4 bg-blue-50">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Transfer Type</label>
                      <select
                        value={transfer.type}
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "type", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      >
                        <option value="">Select Type</option>
                        <option value="Airport Transfer">Airport Transfer</option>
                        <option value="Hotel Transfer">Hotel Transfer</option>
                        <option value="Sightseeing Transfer">Sightseeing Transfer</option>
                        <option value="Inter-city Transfer">Inter-city Transfer</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Vehicle Type</label>
                      <select
                        value={transfer.vehicleType}
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "vehicleType", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      >
                        <option value="">Select Vehicle</option>
                        <option value="Sedan">Sedan</option>
                        <option value="SUV">SUV</option>
                        <option value="Van">Van</option>
                        <option value="Bus">Bus</option>
                        <option value="Luxury Car">Luxury Car</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">People Allowed</label>
                      <input
                        type="number"
                        value={transfer.peopleAllowed}
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "peopleAllowed", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">From</label>
                      <input
                        value={transfer.from}
                        placeholder="Departure location"
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "from", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">To</label>
                      <input
                        value={transfer.to}
                        placeholder="Destination"
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "to", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Distance</label>
                      <input
                        value={transfer.distance}
                        placeholder="e.g., 25 km"
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "distance", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Departure Time</label>
                      <input
                        type="time"
                        value={transfer.departureTime}
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "departureTime", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Arrival Time</label>
                      <input
                        type="time"
                        value={transfer.arrivalTime}
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "arrivalTime", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Duration</label>
                      <input
                        value={transfer.duration}
                        placeholder="e.g., 1 hour"
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "duration", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Price</label>
                      <input
                        value={transfer.price}
                        placeholder="e.g., $30"
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "price", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Flights */}
            <div>
              <h3 className="text-lg font-semibold mb-4 text-[#541C9C] flex justify-between items-center">
                Flights
                <button
                  type="button"
                  onClick={() => addFlight(dayIndex)}
                  className="bg-[#541C9C] text-white text-sm px-3 py-1 rounded"
                >
                  + Add Flight
                </button>
              </h3>
              {day.flights.map((flight, flightIndex) => (
                <div key={flightIndex} className="border p-4 rounded space-y-3 mb-4 bg-green-50">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Flight Number</label>
                      <input
                        value={flight.flightNumber}
                        placeholder="e.g., AI 101"
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "flightNumber", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Airline</label>
                      <input
                        value={flight.airline}
                        placeholder="e.g., Air India"
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "airline", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Class</label>
                      <select
                        value={flight.class}
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "class", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      >
                        <option value="">Select Class</option>
                        <option value="Economy">Economy</option>
                        <option value="Premium Economy">Premium Economy</option>
                        <option value="Business">Business</option>
                        <option value="First">First</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">From</label>
                      <input
                        value={flight.from}
                        placeholder="Departure city/airport"
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "from", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">To</label>
                      <input
                        value={flight.to}
                        placeholder="Arrival city/airport"
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "to", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Duration</label>
                      <input
                        value={flight.duration}
                        placeholder="e.g., 2h 30m"
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "duration", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Departure Time</label>
                      <input
                        type="datetime-local"
                        value={flight.departureTime}
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "departureTime", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Arrival Time</label>
                      <input
                        type="datetime-local"
                        value={flight.arrivalTime}
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "arrivalTime", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Baggage</label>
                      <input
                        value={flight.baggage}
                        placeholder="e.g., 20kg checked, 7kg cabin"
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "baggage", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Price</label>
                      <input
                        value={flight.price}
                        placeholder="e.g., $250"
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "price", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Accommodations */}
            <div>
              <h3 className="text-lg font-semibold mb-4 text-[#541C9C] flex justify-between items-center">
                Accommodations
                <button
                  type="button"
                  onClick={() => addAccommodation(dayIndex)}
                  className="bg-[#541C9C] text-white text-sm px-3 py-1 rounded"
                >
                  + Add Accommodation
                </button>
              </h3>
              {day.accommodations.map((accommodation, accommodationIndex) => (
                <div key={accommodationIndex} className="border p-4 rounded space-y-3 mb-4 bg-purple-50">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Hotel Name</label>
                      <input
                        value={accommodation.name}
                        placeholder="e.g., Grand Hotel"
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "name", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Type</label>
                      <select
                        value={accommodation.type}
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "type", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      >
                        <option value="">Select Type</option>
                        <option value="Hotel">Hotel</option>
                        <option value="Resort">Resort</option>
                        <option value="Guesthouse">Guesthouse</option>
                        <option value="Apartment">Apartment</option>
                        <option value="Villa">Villa</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Rating</label>
                      <select
                        value={accommodation.rating}
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "rating", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      >
                        <option value="">Select Rating</option>
                        <option value="3 Star">3 Star</option>
                        <option value="4 Star">4 Star</option>
                        <option value="5 Star">5 Star</option>
                        <option value="Luxury">Luxury</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Location</label>
                      <input
                        value={accommodation.location}
                        placeholder="City, Area"
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "location", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Room Type</label>
                      <select
                        value={accommodation.roomType}
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "roomType", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      >
                        <option value="">Select Room Type</option>
                        <option value="Standard Room">Standard Room</option>
                        <option value="Deluxe Room">Deluxe Room</option>
                        <option value="Suite">Suite</option>
                        <option value="Executive Room">Executive Room</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Price per Night</label>
                      <input
                        value={accommodation.price}
                        placeholder="e.g., $120"
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "price", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Check-in Date</label>
                      <input
                        type="date"
                        value={accommodation.checkIn}
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "checkIn", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Check-out Date</label>
                      <input
                        type="date"
                        value={accommodation.checkOut}
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "checkOut", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Image URL</label>
                      <input
                        value={accommodation.image}
                        placeholder="https://..."
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "image", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div className="lg:col-span-3">
                      <label className="block text-sm font-medium text-gray-700">Amenities</label>
                      <textarea
                        value={accommodation.amenities}
                        placeholder="WiFi, Pool, Gym, Spa, Restaurant..."
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "amenities", e.target.value)
                        }
                        rows={2}
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      ></textarea>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}

        <button
          type="submit"
          className="w-full bg-[#541C9C] text-white py-3 rounded hover:bg-[#680099] font-semibold"
        >
          Save Itinerary
        </button>
      </form>
    </div>
  );
};

export default ItineraryForm;
