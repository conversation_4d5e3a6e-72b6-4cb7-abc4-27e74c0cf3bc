import React, { useState } from "react";

const ItineraryForm = () => {
  // Basic Information
  const [basicInfo, setBasicInfo] = useState({
    username: "",
    destination: "",
    numDays: 1,
    numNights: 0,
    departureFrom: "",
    departureDate: "",
    arrival: "",
    numTravellers: 1,
  });

  // Days structure
  const [days, setDays] = useState([
    {
      morning: "",
      afternoon: "",
      evening: "",
      activities: [],
      transfers: [],
      flights: [],
      accommodations: [],
    },
  ]);

  // Flight Summary
  const [flightSummary, setFlightSummary] = useState([]);

  // Hotel Bookings
  const [hotelBookings, setHotelBookings] = useState([]);

  // Important Notes
  const [importantNotes, setImportantNotes] = useState([]);

  // Scope of Service
  const [scopeOfService, setScopeOfService] = useState([]);

  // Inclusion Summary
  const [inclusionSummary, setInclusionSummary] = useState([]);

  // Activity Table
  const [activityTable, setActivityTable] = useState([]);

  // Payment Plan
  const [paymentPlan, setPaymentPlan] = useState({
    totalAmount: "",
    tcsCollected: false,
    installments: [],
  });

  // Visa Details
  const [visaDetails, setVisaDetails] = useState([]);

  const handleBasicInfoChange = (field, value) => {
    setBasicInfo(prev => ({
      ...prev,
      [field]: value,
      ...(field === 'numDays' && { numNights: Math.max(0, parseInt(value) - 1) })
    }));

    if (field === 'numDays') {
      const newNumDays = parseInt(value);
      const updatedDays = [...days];

      // add new day objects if needed
      while (updatedDays.length < newNumDays) {
        updatedDays.push({
          morning: "",
          afternoon: "",
          evening: "",
          activities: [],
          transfers: [],
          flights: [],
          accommodations: [],
        });
      }
      // remove days if fewer now
      while (updatedDays.length > newNumDays) {
        updatedDays.pop();
      }
      setDays(updatedDays);
    }
  };

  const updateDaySchedule = (dayIndex, period, value) => {
    const updatedDays = [...days];
    updatedDays[dayIndex][period] = value;
    setDays(updatedDays);
  };

  const addActivity = (dayIndex) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].activities.push({
      id: "",
      name: "",
      description: "",
      price: "",
      duration: "",
      startTime: "",
      endTime: "",
      location: "",
      image: "",
      inclusions: "",
      exclusions: "",
    });
    setDays(updatedDays);
  };

  const updateActivity = (dayIndex, activityIndex, field, value) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].activities[activityIndex][field] = value;
    setDays(updatedDays);
  };

  const addTransfer = (dayIndex) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].transfers.push({
      type: "",
      vehicleType: "",
      from: "",
      to: "",
      departureTime: "",
      arrivalTime: "",
      price: "",
      peopleAllowed: "",
      duration: "",
      distance: "",
    });
    setDays(updatedDays);
  };

  const updateTransfer = (dayIndex, transferIndex, field, value) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].transfers[transferIndex][field] = value;
    setDays(updatedDays);
  };

  const addFlight = (dayIndex) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].flights.push({
      flightNumber: "",
      airline: "",
      from: "",
      to: "",
      departureTime: "",
      arrivalTime: "",
      price: "",
      duration: "",
      class: "",
      baggage: "",
    });
    setDays(updatedDays);
  };

  const addAccommodation = (dayIndex) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].accommodations.push({
      name: "",
      type: "",
      location: "",
      checkIn: "",
      checkOut: "",
      roomType: "",
      price: "",
      rating: "",
      amenities: "",
      image: "",
    });
    setDays(updatedDays);
  };

  const updateFlight = (dayIndex, flightIndex, field, value) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].flights[flightIndex][field] = value;
    setDays(updatedDays);
  };

  const updateAccommodation = (dayIndex, accommodationIndex, field, value) => {
    const updatedDays = [...days];
    updatedDays[dayIndex].accommodations[accommodationIndex][field] = value;
    setDays(updatedDays);
  };

  // Flight Summary handlers
  const addFlightSummary = () => {
    setFlightSummary([...flightSummary, {
      from: "",
      to: "",
      date: "",
      flightNumber: "",
      airline: "",
      price: "",
    }]);
  };

  const updateFlightSummary = (index, field, value) => {
    const updated = [...flightSummary];
    updated[index][field] = value;
    setFlightSummary(updated);
  };

  // Hotel Bookings handlers
  const addHotelBooking = () => {
    setHotelBookings([...hotelBookings, {
      city: "",
      checkIn: "",
      checkOut: "",
      nights: "",
      hotelName: "",
    }]);
  };

  const updateHotelBooking = (index, field, value) => {
    const updated = [...hotelBookings];
    updated[index][field] = value;
    setHotelBookings(updated);
  };

  // Important Notes handlers
  const addImportantNote = () => {
    setImportantNotes([...importantNotes, {
      point: "",
      details: "",
    }]);
  };

  const updateImportantNote = (index, field, value) => {
    const updated = [...importantNotes];
    updated[index][field] = value;
    setImportantNotes(updated);
  };

  // Scope of Service handlers
  const addScopeOfService = () => {
    setScopeOfService([...scopeOfService, {
      service: "",
      details: "",
    }]);
  };

  const updateScopeOfService = (index, field, value) => {
    const updated = [...scopeOfService];
    updated[index][field] = value;
    setScopeOfService(updated);
  };

  // Inclusion Summary handlers
  const addInclusionSummary = () => {
    setInclusionSummary([...inclusionSummary, {
      category: "",
      count: "",
      details: "",
      status: "",
      comment: "",
    }]);
  };

  const updateInclusionSummary = (index, field, value) => {
    const updated = [...inclusionSummary];
    updated[index][field] = value;
    setInclusionSummary(updated);
  };

  // Activity Table handlers
  const addActivityTable = () => {
    setActivityTable([...activityTable, {
      city: "",
      activity: "",
      type: "",
      time: "",
    }]);
  };

  const updateActivityTable = (index, field, value) => {
    const updated = [...activityTable];
    updated[index][field] = value;
    setActivityTable(updated);
  };

  // Payment Plan handlers
  const addInstallment = () => {
    setPaymentPlan(prev => ({
      ...prev,
      installments: [...prev.installments, {
        amount: "",
        dueDate: "",
        status: "pending",
      }]
    }));
  };

  const updateInstallment = (index, field, value) => {
    setPaymentPlan(prev => ({
      ...prev,
      installments: prev.installments.map((inst, i) =>
        i === index ? { ...inst, [field]: value } : inst
      )
    }));
  };

  const updatePaymentPlan = (field, value) => {
    setPaymentPlan(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Visa Details handlers
  const addVisaDetail = () => {
    setVisaDetails([...visaDetails, {
      type: "",
      validity: "",
      processingDate: "",
    }]);
  };

  const updateVisaDetail = (index, field, value) => {
    const updated = [...visaDetails];
    updated[index][field] = value;
    setVisaDetails(updated);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const completeItinerary = {
      basicInfo,
      days,
      flightSummary,
      hotelBookings,
      importantNotes,
      scopeOfService,
      inclusionSummary,
      activityTable,
      paymentPlan,
      visaDetails,
    };
    console.log("Complete Itinerary:", completeItinerary);
    alert("Itinerary Saved Successfully!");
  };

  return (
    <div className="max-w-7xl mx-auto p-8 bg-white rounded shadow space-y-10">
      <h1 className="text-3xl font-bold text-[#541C9C] text-center mb-6">
        Create Travel Itinerary
      </h1>

      {/* Basic Information Section */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h2 className="text-2xl font-bold text-[#321E5D] mb-6">Basic Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Username</label>
            <input
              type="text"
              value={basicInfo.username}
              onChange={(e) => handleBasicInfoChange('username', e.target.value)}
              className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
              placeholder="Enter username"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Destination</label>
            <input
              type="text"
              value={basicInfo.destination}
              onChange={(e) => handleBasicInfoChange('destination', e.target.value)}
              className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
              placeholder="Enter destination"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Number of Days</label>
            <input
              type="number"
              min="1"
              value={basicInfo.numDays}
              onChange={(e) => handleBasicInfoChange('numDays', e.target.value)}
              className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Number of Nights</label>
            <input
              type="number"
              min="0"
              value={basicInfo.numNights}
              onChange={(e) => handleBasicInfoChange('numNights', e.target.value)}
              className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Departure From</label>
            <input
              type="text"
              value={basicInfo.departureFrom}
              onChange={(e) => handleBasicInfoChange('departureFrom', e.target.value)}
              className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
              placeholder="Enter departure city"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Departure Date</label>
            <input
              type="date"
              value={basicInfo.departureDate}
              onChange={(e) => handleBasicInfoChange('departureDate', e.target.value)}
              className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Arrival</label>
            <input
              type="text"
              value={basicInfo.arrival}
              onChange={(e) => handleBasicInfoChange('arrival', e.target.value)}
              className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
              placeholder="Enter arrival city"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Number of Travellers</label>
            <input
              type="number"
              min="1"
              value={basicInfo.numTravellers}
              onChange={(e) => handleBasicInfoChange('numTravellers', e.target.value)}
              className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
            />
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Day-by-Day Itinerary */}
        <div className="bg-blue-50 p-6 rounded-lg">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-[#321E5D]">Day-by-Day Itinerary</h2>
            <button
              type="button"
              onClick={() => handleBasicInfoChange('numDays', basicInfo.numDays + 1)}
              className="bg-[#541C9C] text-white px-4 py-2 rounded hover:bg-[#680099]"
            >
              Add Day
            </button>
          </div>

          {days.map((day, dayIndex) => (
            <div key={dayIndex} className="bg-white border border-gray-200 p-6 rounded-lg mb-6 space-y-6">
              <h3 className="text-xl font-bold text-[#321E5D]">
                Day {dayIndex + 1}
              </h3>

              {/* Day Schedule */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Morning</label>
                  <textarea
                    value={day.morning}
                    onChange={(e) => updateDaySchedule(dayIndex, 'morning', e.target.value)}
                    rows={3}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Morning activities..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Afternoon</label>
                  <textarea
                    value={day.afternoon}
                    onChange={(e) => updateDaySchedule(dayIndex, 'afternoon', e.target.value)}
                    rows={3}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Afternoon activities..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Evening</label>
                  <textarea
                    value={day.evening}
                    onChange={(e) => updateDaySchedule(dayIndex, 'evening', e.target.value)}
                    rows={3}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Evening activities..."
                  />
                </div>
              </div>

              {/* Activities */}
              <div>
                <h4 className="text-lg font-semibold mb-4 text-[#541C9C] flex justify-between items-center">
                  Activities
                  <button
                    type="button"
                    onClick={() => addActivity(dayIndex)}
                    className="bg-[#541C9C] text-white text-sm px-3 py-1 rounded"
                  >
                    + Add Activity
                  </button>
                </h4>

              {day.activities.map((activity, activityIndex) => (
                <div
                  key={activityIndex}
                  className="p-4 border rounded space-y-3 mb-4 bg-gray-50"
                >
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Activity ID
                      </label>
                      <input
                        value={activity.id}
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "id",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Activity Name
                      </label>
                      <input
                        value={activity.name}
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "name",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Location
                      </label>
                      <input
                        value={activity.location}
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "location",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Start Time
                      </label>
                      <input
                        type="time"
                        value={activity.startTime}
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "startTime",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        End Time
                      </label>
                      <input
                        type="time"
                        value={activity.endTime}
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "endTime",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Duration
                      </label>
                      <input
                        value={activity.duration}
                        placeholder="e.g., 2 hours"
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "duration",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Price
                      </label>
                      <input
                        value={activity.price}
                        placeholder="e.g., $50"
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "price",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Image URL
                      </label>
                      <input
                        value={activity.image}
                        placeholder="https://..."
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "image",
                            e.target.value
                          )
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div className="lg:col-span-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Description
                      </label>
                      <textarea
                        value={activity.description}
                        onChange={(e) =>
                          updateActivity(
                            dayIndex,
                            activityIndex,
                            "description",
                            e.target.value
                          )
                        }
                        rows={2}
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      ></textarea>
                    </div>
                    <div className="lg:col-span-3">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            Inclusions
                          </label>
                          <textarea
                            value={activity.inclusions}
                            placeholder="What's included..."
                            onChange={(e) =>
                              updateActivity(
                                dayIndex,
                                activityIndex,
                                "inclusions",
                                e.target.value
                              )
                            }
                            rows={2}
                            className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                          ></textarea>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            Exclusions
                          </label>
                          <textarea
                            value={activity.exclusions}
                            placeholder="What's not included..."
                            onChange={(e) =>
                              updateActivity(
                                dayIndex,
                                activityIndex,
                                "exclusions",
                                e.target.value
                              )
                            }
                            rows={2}
                            className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                          ></textarea>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

              {/* Transfer */}
              <div>
                <h4 className="text-lg font-semibold mb-4 text-[#541C9C] flex justify-between items-center">
                  Transfers
                  <button
                    type="button"
                    onClick={() => addTransfer(dayIndex)}
                    className="bg-[#541C9C] text-white text-sm px-3 py-1 rounded"
                  >
                    + Add Transfer
                  </button>
                </h4>
              {day.transfers.map((transfer, transferIndex) => (
                <div key={transferIndex} className="border p-4 rounded space-y-3 mb-4 bg-blue-50">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Transfer Type</label>
                      <select
                        value={transfer.type}
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "type", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      >
                        <option value="">Select Type</option>
                        <option value="Airport Transfer">Airport Transfer</option>
                        <option value="Hotel Transfer">Hotel Transfer</option>
                        <option value="Sightseeing Transfer">Sightseeing Transfer</option>
                        <option value="Inter-city Transfer">Inter-city Transfer</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Vehicle Type</label>
                      <select
                        value={transfer.vehicleType}
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "vehicleType", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      >
                        <option value="">Select Vehicle</option>
                        <option value="Sedan">Sedan</option>
                        <option value="SUV">SUV</option>
                        <option value="Van">Van</option>
                        <option value="Bus">Bus</option>
                        <option value="Luxury Car">Luxury Car</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">People Allowed</label>
                      <input
                        type="number"
                        value={transfer.peopleAllowed}
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "peopleAllowed", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">From</label>
                      <input
                        value={transfer.from}
                        placeholder="Departure location"
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "from", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">To</label>
                      <input
                        value={transfer.to}
                        placeholder="Destination"
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "to", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Distance</label>
                      <input
                        value={transfer.distance}
                        placeholder="e.g., 25 km"
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "distance", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Departure Time</label>
                      <input
                        type="time"
                        value={transfer.departureTime}
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "departureTime", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Arrival Time</label>
                      <input
                        type="time"
                        value={transfer.arrivalTime}
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "arrivalTime", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Duration</label>
                      <input
                        value={transfer.duration}
                        placeholder="e.g., 1 hour"
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "duration", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Price</label>
                      <input
                        value={transfer.price}
                        placeholder="e.g., $30"
                        onChange={(e) =>
                          updateTransfer(dayIndex, transferIndex, "price", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>

              {/* Flights */}
              <div>
                <h4 className="text-lg font-semibold mb-4 text-[#541C9C] flex justify-between items-center">
                  Flights
                  <button
                    type="button"
                    onClick={() => addFlight(dayIndex)}
                    className="bg-[#541C9C] text-white text-sm px-3 py-1 rounded"
                  >
                    + Add Flight
                  </button>
                </h4>
              {day.flights.map((flight, flightIndex) => (
                <div key={flightIndex} className="border p-4 rounded space-y-3 mb-4 bg-green-50">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Flight Number</label>
                      <input
                        value={flight.flightNumber}
                        placeholder="e.g., AI 101"
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "flightNumber", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Airline</label>
                      <input
                        value={flight.airline}
                        placeholder="e.g., Air India"
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "airline", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Class</label>
                      <select
                        value={flight.class}
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "class", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      >
                        <option value="">Select Class</option>
                        <option value="Economy">Economy</option>
                        <option value="Premium Economy">Premium Economy</option>
                        <option value="Business">Business</option>
                        <option value="First">First</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">From</label>
                      <input
                        value={flight.from}
                        placeholder="Departure city/airport"
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "from", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">To</label>
                      <input
                        value={flight.to}
                        placeholder="Arrival city/airport"
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "to", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Duration</label>
                      <input
                        value={flight.duration}
                        placeholder="e.g., 2h 30m"
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "duration", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Departure Time</label>
                      <input
                        type="datetime-local"
                        value={flight.departureTime}
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "departureTime", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Arrival Time</label>
                      <input
                        type="datetime-local"
                        value={flight.arrivalTime}
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "arrivalTime", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Baggage</label>
                      <input
                        value={flight.baggage}
                        placeholder="e.g., 20kg checked, 7kg cabin"
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "baggage", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Price</label>
                      <input
                        value={flight.price}
                        placeholder="e.g., $250"
                        onChange={(e) =>
                          updateFlight(dayIndex, flightIndex, "price", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>

              {/* Accommodations */}
              <div>
                <h4 className="text-lg font-semibold mb-4 text-[#541C9C] flex justify-between items-center">
                  Accommodations
                  <button
                    type="button"
                    onClick={() => addAccommodation(dayIndex)}
                    className="bg-[#541C9C] text-white text-sm px-3 py-1 rounded"
                  >
                    + Add Accommodation
                  </button>
                </h4>
              {day.accommodations.map((accommodation, accommodationIndex) => (
                <div key={accommodationIndex} className="border p-4 rounded space-y-3 mb-4 bg-purple-50">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Hotel Name</label>
                      <input
                        value={accommodation.name}
                        placeholder="e.g., Grand Hotel"
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "name", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Type</label>
                      <select
                        value={accommodation.type}
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "type", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      >
                        <option value="">Select Type</option>
                        <option value="Hotel">Hotel</option>
                        <option value="Resort">Resort</option>
                        <option value="Guesthouse">Guesthouse</option>
                        <option value="Apartment">Apartment</option>
                        <option value="Villa">Villa</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Rating</label>
                      <select
                        value={accommodation.rating}
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "rating", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      >
                        <option value="">Select Rating</option>
                        <option value="3 Star">3 Star</option>
                        <option value="4 Star">4 Star</option>
                        <option value="5 Star">5 Star</option>
                        <option value="Luxury">Luxury</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Location</label>
                      <input
                        value={accommodation.location}
                        placeholder="City, Area"
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "location", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Room Type</label>
                      <select
                        value={accommodation.roomType}
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "roomType", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      >
                        <option value="">Select Room Type</option>
                        <option value="Standard Room">Standard Room</option>
                        <option value="Deluxe Room">Deluxe Room</option>
                        <option value="Suite">Suite</option>
                        <option value="Executive Room">Executive Room</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Price per Night</label>
                      <input
                        value={accommodation.price}
                        placeholder="e.g., $120"
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "price", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Check-in Date</label>
                      <input
                        type="date"
                        value={accommodation.checkIn}
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "checkIn", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Check-out Date</label>
                      <input
                        type="date"
                        value={accommodation.checkOut}
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "checkOut", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Image URL</label>
                      <input
                        value={accommodation.image}
                        placeholder="https://..."
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "image", e.target.value)
                        }
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      />
                    </div>
                    <div className="lg:col-span-3">
                      <label className="block text-sm font-medium text-gray-700">Amenities</label>
                      <textarea
                        value={accommodation.amenities}
                        placeholder="WiFi, Pool, Gym, Spa, Restaurant..."
                        onChange={(e) =>
                          updateAccommodation(dayIndex, accommodationIndex, "amenities", e.target.value)
                        }
                        rows={2}
                        className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                      ></textarea>
                    </div>
                  </div>
                </div>
              ))}
              </div>
            </div>
          ))}
        </div>

        {/* Flight Summary Section */}
        <div className="bg-green-50 p-6 rounded-lg">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-[#321E5D]">Flight Summary</h2>
            <button
              type="button"
              onClick={addFlightSummary}
              className="bg-[#541C9C] text-white px-4 py-2 rounded hover:bg-[#680099]"
            >
              Add Summary
            </button>
          </div>
          {flightSummary.map((flight, index) => (
            <div key={index} className="bg-white p-4 rounded border mb-4">
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">From</label>
                  <input
                    value={flight.from}
                    onChange={(e) => updateFlightSummary(index, 'from', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Departure city"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">To</label>
                  <input
                    value={flight.to}
                    onChange={(e) => updateFlightSummary(index, 'to', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Arrival city"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
                  <input
                    type="date"
                    value={flight.date}
                    onChange={(e) => updateFlightSummary(index, 'date', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Flight Number</label>
                  <input
                    value={flight.flightNumber}
                    onChange={(e) => updateFlightSummary(index, 'flightNumber', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="e.g., AI 101"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Airline</label>
                  <input
                    value={flight.airline}
                    onChange={(e) => updateFlightSummary(index, 'airline', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="e.g., Air India"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Price</label>
                  <input
                    value={flight.price}
                    onChange={(e) => updateFlightSummary(index, 'price', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="e.g., $250"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Hotel Bookings Section */}
        <div className="bg-purple-50 p-6 rounded-lg">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-[#321E5D]">Hotel Bookings</h2>
            <button
              type="button"
              onClick={addHotelBooking}
              className="bg-[#541C9C] text-white px-4 py-2 rounded hover:bg-[#680099]"
            >
              Add Hotel
            </button>
          </div>
          {hotelBookings.map((hotel, index) => (
            <div key={index} className="bg-white p-4 rounded border mb-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">City</label>
                  <input
                    value={hotel.city}
                    onChange={(e) => updateHotelBooking(index, 'city', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Enter city"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Check-in</label>
                  <input
                    type="date"
                    value={hotel.checkIn}
                    onChange={(e) => updateHotelBooking(index, 'checkIn', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Check-out</label>
                  <input
                    type="date"
                    value={hotel.checkOut}
                    onChange={(e) => updateHotelBooking(index, 'checkOut', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Nights</label>
                  <input
                    type="number"
                    value={hotel.nights}
                    onChange={(e) => updateHotelBooking(index, 'nights', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Number of nights"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Hotel Name</label>
                  <input
                    value={hotel.hotelName}
                    onChange={(e) => updateHotelBooking(index, 'hotelName', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Enter hotel name"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Important Notes Section */}
        <div className="bg-yellow-50 p-6 rounded-lg">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-[#321E5D]">Important Notes</h2>
            <button
              type="button"
              onClick={addImportantNote}
              className="bg-[#541C9C] text-white px-4 py-2 rounded hover:bg-[#680099]"
            >
              Add Point
            </button>
          </div>
          {importantNotes.map((note, index) => (
            <div key={index} className="bg-white p-4 rounded border mb-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Point</label>
                  <input
                    value={note.point}
                    onChange={(e) => updateImportantNote(index, 'point', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Enter important point"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Details</label>
                  <textarea
                    value={note.details}
                    onChange={(e) => updateImportantNote(index, 'details', e.target.value)}
                    rows={3}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Enter corresponding details"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Scope of Service Section */}
        <div className="bg-indigo-50 p-6 rounded-lg">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-[#321E5D]">Scope of Service</h2>
            <button
              type="button"
              onClick={addScopeOfService}
              className="bg-[#541C9C] text-white px-4 py-2 rounded hover:bg-[#680099]"
            >
              Add Service
            </button>
          </div>
          {scopeOfService.map((service, index) => (
            <div key={index} className="bg-white p-4 rounded border mb-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Service</label>
                  <input
                    value={service.service}
                    onChange={(e) => updateScopeOfService(index, 'service', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Enter service name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Details</label>
                  <textarea
                    value={service.details}
                    onChange={(e) => updateScopeOfService(index, 'details', e.target.value)}
                    rows={3}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Enter service details"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Inclusion Summary Section */}
        <div className="bg-teal-50 p-6 rounded-lg">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-[#321E5D]">Inclusion Summary</h2>
            <button
              type="button"
              onClick={addInclusionSummary}
              className="bg-[#541C9C] text-white px-4 py-2 rounded hover:bg-[#680099]"
            >
              Add Inclusion
            </button>
          </div>
          {inclusionSummary.map((inclusion, index) => (
            <div key={index} className="bg-white p-4 rounded border mb-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                  <input
                    value={inclusion.category}
                    onChange={(e) => updateInclusionSummary(index, 'category', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="e.g., Accommodation"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Count</label>
                  <input
                    type="number"
                    value={inclusion.count}
                    onChange={(e) => updateInclusionSummary(index, 'count', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Quantity"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Details</label>
                  <input
                    value={inclusion.details}
                    onChange={(e) => updateInclusionSummary(index, 'details', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Enter details"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                  <select
                    value={inclusion.status}
                    onChange={(e) => updateInclusionSummary(index, 'status', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                  >
                    <option value="">Select Status</option>
                    <option value="Confirmed">Confirmed</option>
                    <option value="Pending">Pending</option>
                    <option value="Cancelled">Cancelled</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Comment</label>
                  <input
                    value={inclusion.comment}
                    onChange={(e) => updateInclusionSummary(index, 'comment', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Additional comments"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Activity Table Section */}
        <div className="bg-orange-50 p-6 rounded-lg">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-[#321E5D]">Activity Table</h2>
            <button
              type="button"
              onClick={addActivityTable}
              className="bg-[#541C9C] text-white px-4 py-2 rounded hover:bg-[#680099]"
            >
              Add Activity
            </button>
          </div>
          {activityTable.map((activity, index) => (
            <div key={index} className="bg-white p-4 rounded border mb-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">City</label>
                  <input
                    value={activity.city}
                    onChange={(e) => updateActivityTable(index, 'city', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Enter city"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Activity</label>
                  <input
                    value={activity.activity}
                    onChange={(e) => updateActivityTable(index, 'activity', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Enter activity name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
                  <select
                    value={activity.type}
                    onChange={(e) => updateActivityTable(index, 'type', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                  >
                    <option value="">Select Type</option>
                    <option value="Sightseeing">Sightseeing</option>
                    <option value="Adventure">Adventure</option>
                    <option value="Cultural">Cultural</option>
                    <option value="Entertainment">Entertainment</option>
                    <option value="Shopping">Shopping</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Time</label>
                  <input
                    type="time"
                    value={activity.time}
                    onChange={(e) => updateActivityTable(index, 'time', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Payment Plan Section */}
        <div className="bg-red-50 p-6 rounded-lg">
          <h2 className="text-2xl font-bold text-[#321E5D] mb-6">Payment Plan</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Total Amount</label>
              <input
                value={paymentPlan.totalAmount}
                onChange={(e) => updatePaymentPlan('totalAmount', e.target.value)}
                className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                placeholder="Enter total amount"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">TCS</label>
              <select
                value={paymentPlan.tcsCollected}
                onChange={(e) => updatePaymentPlan('tcsCollected', e.target.value === 'true')}
                className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
              >
                <option value={false}>Not Collected</option>
                <option value={true}>Collected</option>
              </select>
            </div>
          </div>

          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-[#541C9C]">Installment Amount Due Table</h3>
            <button
              type="button"
              onClick={addInstallment}
              className="bg-[#541C9C] text-white px-4 py-2 rounded hover:bg-[#680099]"
            >
              Add Installment
            </button>
          </div>

          {paymentPlan.installments.map((installment, index) => (
            <div key={index} className="bg-white p-4 rounded border mb-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Amount</label>
                  <input
                    value={installment.amount}
                    onChange={(e) => updateInstallment(index, 'amount', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="Enter amount"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Due Date</label>
                  <input
                    type="date"
                    value={installment.dueDate}
                    onChange={(e) => updateInstallment(index, 'dueDate', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                  <select
                    value={installment.status}
                    onChange={(e) => updateInstallment(index, 'status', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                  >
                    <option value="pending">Pending</option>
                    <option value="paid">Paid</option>
                    <option value="overdue">Overdue</option>
                  </select>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Visa Details Section */}
        <div className="bg-pink-50 p-6 rounded-lg">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-[#321E5D]">Visa Details</h2>
            <button
              type="button"
              onClick={addVisaDetail}
              className="bg-[#541C9C] text-white px-4 py-2 rounded hover:bg-[#680099]"
            >
              Add Visa
            </button>
          </div>
          {visaDetails.map((visa, index) => (
            <div key={index} className="bg-white p-4 rounded border mb-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
                  <select
                    value={visa.type}
                    onChange={(e) => updateVisaDetail(index, 'type', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                  >
                    <option value="">Select Visa Type</option>
                    <option value="Tourist">Tourist</option>
                    <option value="Business">Business</option>
                    <option value="Transit">Transit</option>
                    <option value="Student">Student</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Validity</label>
                  <input
                    value={visa.validity}
                    onChange={(e) => updateVisaDetail(index, 'validity', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                    placeholder="e.g., 30 days"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Processing Date</label>
                  <input
                    type="date"
                    value={visa.processingDate}
                    onChange={(e) => updateVisaDetail(index, 'processingDate', e.target.value)}
                    className="w-full rounded border-gray-300 focus:ring-[#541C9C] focus:border-[#541C9C]"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        <button
          type="submit"
          className="w-full bg-[#541C9C] text-white py-3 rounded hover:bg-[#680099] font-semibold"
        >
          Generate Itinerary
        </button>
      </form>
    </div>
  );
};

export default ItineraryForm;
