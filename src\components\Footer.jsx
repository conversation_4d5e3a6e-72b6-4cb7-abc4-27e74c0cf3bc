import React from "react";
import vigoviaLogo from "../assets/logo.PNG"; 

const Footer = () => {
  return (
    <footer className="bg-gradient-to-r from-purple-200 to-purple-100 text-gray-700 pt-10">
      <div className="max-w-7xl mx-auto grid grid-cols-2 md:grid-cols-5 gap-6 px-6">
        <div>
          <h3 className="font-bold mb-2">Our offerings</h3>
          <ul>
            <li>Holidays</li>
            <li>Visa</li>
            <li>Forex</li>
            <li>Hotels</li>
            <li>Flights</li>
          </ul>
        </div>
        <div>
          <h3 className="font-bold mb-2">Popular destinations</h3>
          <ul>
            <li>Dubai</li>
            <li>Bali</li>
            <li>Thailand</li>
            <li>Singapore</li>
            <li>Malaysia</li>
          </ul>
        </div>
        <div>
          <h3 className="font-bold mb-2">Vigovia Specials</h3>
          <ul>
            <li>Group Tours</li>
            <li>Featured Experiences</li>
            <li>Backpackers Club</li>
            <li>Offline Events</li>
          </ul>
        </div>
        <div>
          <h3 className="font-bold mb-2">Company</h3>
          <ul>
            <li>About Us</li>
            <li>Careers</li>
            <li>Blog</li>
            <li>Investor Relations</li>
            <li>Partner Portal</li>
          </ul>
        </div>
        <div>
          <h3 className="font-bold mb-2">Support</h3>
          <ul>
            <li>+91-858xxxx661</li>
            <li><EMAIL></li>
            <li>HIT-CBD, North 24 Parganas</li>
          </ul>
        </div>
      </div>

      <div className="mt-8 border-t pt-4 px-6 text-sm flex flex-col md:flex-row items-center justify-between">
        <img src={vigoviaLogo} alt="Vigovia" className="h-6 mb-2 md:mb-0" />
        <p>&copy; 2025 Vigovia Technologies Pvt. Ltd. All rights reserved.</p>
        <div className="flex gap-2">
          <a href="#">Privacy</a>
          <a href="#">Terms</a>
          <a href="#">Accessibility</a>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
