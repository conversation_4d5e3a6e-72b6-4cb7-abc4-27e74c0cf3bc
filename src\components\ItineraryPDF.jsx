// ItineraryPDF.jsx
import React from 'react';
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Font,
} from '@react-pdf/renderer';

// 🖋️ Optional: Custom font (if desired)
// Font.register({ family: 'Open Sans', src: 'https://fonts.gstatic.com/s/opensans/v18/mem8YaGs126MiZpBA-U1UpcaXcl0Aw.ttf' });

// 📄 Styles
const styles = StyleSheet.create({
  page: {
    padding: 30,
    fontSize: 12,
    fontFamily: 'Helvetica',
  },
  header: {
    textAlign: 'center',
    fontSize: 20,
    marginBottom: 10,
    color: '#541C9C',
  },
  section: {
    marginBottom: 20,
  },
  label: {
    fontWeight: 'bold',
  },
  tableRow: {
    flexDirection: 'row',
    borderBottom: '1px solid #ccc',
    paddingVertical: 4,
  },
  col: {
    width: '25%',
    paddingRight: 10,
  },
  dayBox: {
    border: '1px solid #ccc',
    borderRadius: 6,
    padding: 10,
    marginBottom: 10,
  },
  dayTitle: {
    backgroundColor: '#FBF4FF',
    color: '#321E5D',
    fontSize: 14,
    marginBottom: 4,
    padding: 4,
  },
  infoRow: {
    marginVertical: 2,
  },
  footer: {
    fontSize: 10,
    textAlign: 'center',
    marginTop: 40,
    color: '#999',
  },
});

const ItineraryPDF = ({ itineraryData }) => {
  const {
    basicInfo,
    days,
    flightSummary,
    hotelBookings,
    importantNotes,
    scopeOfService,
    inclusionSummary,
    activityTable,
    paymentPlan,
    visaDetails,
  } = itineraryData;

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* HEADER */}
        <Text style={styles.header}>Vigovia – Travel Itinerary</Text>
        <Text>Hi, {basicInfo?.username || 'Traveller'}!</Text>
        <Text>Plan.Pack.Go</Text>

        {/* TRIP SUMMARY */}
        <View style={styles.section}>
          <Text style={{ fontSize: 14, marginBottom: 6 }}>Trip Summary</Text>
          <View style={styles.tableRow}>
            <Text style={styles.col}><Text style={styles.label}>From:</Text> {basicInfo?.departureFrom || 'N/A'}</Text>
            <Text style={styles.col}><Text style={styles.label}>To:</Text> {basicInfo?.destination || 'N/A'}</Text>
            <Text style={styles.col}><Text style={styles.label}>Departure:</Text> {basicInfo?.departureDate || 'N/A'}</Text>
            <Text style={styles.col}><Text style={styles.label}>Days:</Text> {basicInfo?.numDays || 'N/A'}</Text>
          </View>
          <View style={styles.tableRow}>
            <Text style={styles.col}><Text style={styles.label}>Travellers:</Text> {basicInfo?.numTravellers || 'N/A'}</Text>
            <Text style={styles.col}><Text style={styles.label}>Nights:</Text> {basicInfo?.numNights || 'N/A'}</Text>
          </View>
        </View>

        {/* DAILY PLANS */}
        <View style={styles.section}>
          <Text style={{ fontSize: 14, marginBottom: 6 }}>Daily Itinerary</Text>
          {days?.map((day, idx) => (
            <View key={idx} style={styles.dayBox}>
              <Text style={styles.dayTitle}>Day {idx + 1}</Text>
              {day.morning && (
                <Text style={styles.infoRow}>
                  <Text style={styles.label}>Morning:</Text> {day.morning}
                </Text>
              )}
              {day.afternoon && (
                <Text style={styles.infoRow}>
                  <Text style={styles.label}>Afternoon:</Text> {day.afternoon}
                </Text>
              )}
              {day.evening && (
                <Text style={styles.infoRow}>
                  <Text style={styles.label}>Evening:</Text> {day.evening}
                </Text>
              )}
              
              {/* Activities for this day */}
              {day.activities?.map((activity, i) => (
                <Text key={i} style={styles.infoRow}>
                  <Text style={styles.label}>Activity:</Text> {activity.name} - {activity.description}
                </Text>
              ))}
            </View>
          ))}
        </View>

        {/* FLIGHTS */}
        {flightSummary?.length > 0 && (
          <View style={styles.section}>
            <Text style={{ fontSize: 14, marginBottom: 6 }}>Flight Summary</Text>
            {flightSummary.map((f, i) => (
              <Text key={i} style={styles.infoRow}>
                ✈️ {f.date} – {f.from} to {f.to} – {f.flightNumber} ({f.airline}) - {f.price}
              </Text>
            ))}
          </View>
        )}

        {/* HOTELS */}
        {hotelBookings?.length > 0 && (
          <View style={styles.section}>
            <Text style={{ fontSize: 14, marginBottom: 6 }}>Hotel Bookings</Text>
            {hotelBookings.map((h, i) => (
              <Text key={i} style={styles.infoRow}>
                🏨 {h.city} – {h.hotelName} ({h.checkIn} to {h.checkOut}, {h.nights} nights)
              </Text>
            ))}
          </View>
        )}

        {/* IMPORTANT NOTES */}
        {importantNotes?.length > 0 && (
          <View style={styles.section}>
            <Text style={{ fontSize: 14, marginBottom: 6 }}>Important Notes</Text>
            {importantNotes.map((note, i) => (
              <Text key={i} style={styles.infoRow}>
                📝 {note.point}: {note.details}
              </Text>
            ))}
          </View>
        )}

        {/* ACTIVITY TABLE */}
        {activityTable?.length > 0 && (
          <View style={styles.section}>
            <Text style={{ fontSize: 14, marginBottom: 6 }}>Activity Summary</Text>
            {activityTable.map((activity, i) => (
              <Text key={i} style={styles.infoRow}>
                🎯 {activity.city} – {activity.activity} ({activity.type}) at {activity.time}
              </Text>
            ))}
          </View>
        )}

        {/* PAYMENT PLAN */}
        {(paymentPlan?.totalAmount || paymentPlan?.installments?.length > 0) && (
          <View style={styles.section}>
            <Text style={{ fontSize: 14, marginBottom: 6 }}>Payment Information</Text>
            {paymentPlan.totalAmount && (
              <Text style={styles.infoRow}>
                <Text style={styles.label}>Total Amount:</Text> {paymentPlan.totalAmount}
              </Text>
            )}
            <Text style={styles.infoRow}>
              <Text style={styles.label}>TCS:</Text> {paymentPlan.tcsCollected ? 'Collected' : 'Not Collected'}
            </Text>
            {paymentPlan.installments?.map((inst, i) => (
              <Text key={i} style={styles.infoRow}>
                💰 Installment {i + 1}: {inst.amount} (Due: {inst.dueDate}) - {inst.status}
              </Text>
            ))}
          </View>
        )}

        {/* VISA DETAILS */}
        {visaDetails?.length > 0 && (
          <View style={styles.section}>
            <Text style={{ fontSize: 14, marginBottom: 6 }}>Visa Information</Text>
            {visaDetails.map((visa, i) => (
              <Text key={i} style={styles.infoRow}>
                📋 {visa.type} Visa – Valid for {visa.validity} (Processing: {visa.processingDate})
              </Text>
            ))}
          </View>
        )}

        {/* FOOTER */}
        <Text style={styles.footer}>
          Phone: +91-858xxxx661 | Email: <EMAIL> | © Vigovia Technologies Pvt. Ltd.
        </Text>
      </Page>
    </Document>
  );
};

export default ItineraryPDF;
